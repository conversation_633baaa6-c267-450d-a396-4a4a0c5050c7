{"level":"info","message":"Server is running on http://0.0.0.0:3000","timestamp":"2025-06-14 13:40:44"}
{"level":"info","message":"Environment: development","timestamp":"2025-06-14 13:40:44"}
{"level":"info","message":"Socket.IO enabled on port 3000","timestamp":"2025-06-14 13:40:44"}
{"cors":"http://localhost:5173","level":"info","message":"Socket服务已启动","timestamp":"2025-06-14 13:40:44"}
{"level":"info","message":"Socket.IO service initialized","timestamp":"2025-06-14 13:40:44"}
{"directory":"D:\\Coding\\yuyin\\backend\\uploads","level":"info","message":"创建目录","timestamp":"2025-06-14 13:40:44"}
{"directory":"D:\\Coding\\yuyin\\backend\\temp","level":"info","message":"创建目录","timestamp":"2025-06-14 13:40:44"}
{"level":"info","maxFileAge":86400000,"message":"清理服务已启动","tempDir":"D:\\Coding\\yuyin\\backend\\temp","timestamp":"2025-06-14 13:40:44","uploadDir":"D:\\Coding\\yuyin\\backend\\uploads"}
{"level":"info","message":"Cleanup service initialized","timestamp":"2025-06-14 13:40:44"}
{"level":"info","message":"Created directory: D:\\Coding\\yuyin\\backend\\downloads","timestamp":"2025-06-14 13:40:44"}
{"level":"info","message":"Created directory: D:\\Coding\\yuyin\\backend\\cache","timestamp":"2025-06-14 13:40:44"}
{"level":"info","message":"All services initialized successfully","timestamp":"2025-06-14 13:40:44"}
{"level":"info","message":"开始执行清理任务","timestamp":"2025-06-14 13:40:49"}
{"action":"scheduled_cleanup","filesCount":0,"level":"info","message":"Cleanup operation","timestamp":"2025-06-14 13:40:49","totalSize":0}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:05:43:11 +0000] \"GET /api/health HTTP/1.1\" 200 126 \"-\" \"curl/8.9.1\"","timestamp":"2025-06-14 13:43:11"}
{"level":"info","message":"开始执行清理任务","timestamp":"2025-06-14 14:00:00"}
{"action":"scheduled_cleanup","filesCount":0,"level":"info","message":"Cleanup operation","timestamp":"2025-06-14 14:00:00","totalSize":0}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:07:37 +0000] \"POST /parse-url HTTP/1.1\" 404 82 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:07:37"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:09:08 +0000] \"POST /parse-url HTTP/1.1\" 404 82 \"http://localhost:5173/?ide_webview_request_time=1749881322600\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36\"","timestamp":"2025-06-14 14:09:08"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:09:19 +0000] \"POST /parse-url HTTP/1.1\" 404 82 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:09:19"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:11:39 +0000] \"GET /?ide_webview_request_time=1749881498773 HTTP/1.1\" 200 240 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36\"","timestamp":"2025-06-14 14:11:39"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":"5.87 <EMAIL> 01/01 iCh:/ 交易模式的重要性，找交易模式，做好招牌菜。# 看盘技巧 # 炒股如何避雷 # 股票入门基础知识 # 股民交流 # 被套  https://v.douyin.com/ckLgse3KBd8/ 复制此链接，打开Dou音搜索，直接观看视频！"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:12:03","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:12:03 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/?ide_webview_request_time=1749881513593\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36\"","timestamp":"2025-06-14 14:12:03"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":"5.87 <EMAIL> 01/01 iCh:/ 交易模式的重要性，找交易模式，做好招牌菜。# 看盘技巧 # 炒股如何避雷 # 股票入门基础知识 # 股民交流 # 被套  https://v.douyin.com/ckLgse3KBd8/ 复制此链接，打开Dou音搜索，直接观看视频！"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:12:16","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:12:16 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/?ide_webview_request_time=1749881513593\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36\"","timestamp":"2025-06-14 14:12:16"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":"5.87 <EMAIL> 01/01 iCh:/ 交易模式的重要性，找交易模式，做好招牌菜。# 看盘技巧 # 炒股如何避雷 # 股票入门基础知识 # 股民交流 # 被套  https://v.douyin.com/ckLgse3KBd8/ 复制此链接，打开Dou音搜索，直接观看视频！"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:12:21","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:12:21 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/?ide_webview_request_time=1749881513593\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.98.2 Chrome/132.0.6834.196 Electron/34.2.0 Safari/537.36\"","timestamp":"2025-06-14 14:12:21"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:14:39","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:14:39 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:14:39"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8https://www.youtube.com/watch?v=dQw4w9WgXcQ"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:16:31","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:16:31 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:16:31"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:31","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:31 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:31"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:33","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:33 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:33"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:33","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:33 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:33"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:33","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:33 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:33"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:34","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:34 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:34"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:39","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:39 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:39"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:40","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:40 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:40"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:40","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:40 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:40"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:40","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:40 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:40"}
{"body":{"options":{"denoise":false,"format":"txt","language":"auto","punctuation":true},"url":" https://v.douyin.com/ckLgse3KBd8"},"ip":"127.0.0.1","level":"error","message":"Error occurred: 参数验证失败","method":"POST","params":{},"query":{},"timestamp":"2025-06-14 14:20:40","url":"/api/parse-url","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"127.0.0.1 - - [14/Jun/2025:06:20:40 +0000] \"POST /api/parse-url HTTP/1.1\" 400 111 \"http://localhost:5173/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-06-14 14:20:40"}
